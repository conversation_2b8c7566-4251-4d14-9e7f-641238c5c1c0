import React from 'react'
import { createRoot } from 'react-dom/client'
import App from './App.tsx'
import './index.css'
import { ToastAction } from '@/components/ui/toast'
import { toast } from '@/hooks/use-toast'

// Import Supabase health check - will be wrapped in try-catch during usage
import { checkSupabaseHealth } from '@/integrations/supabase/client'

// Global error handlers for browser extension conflicts
window.addEventListener('error', (event) => {
  // Suppress Grammarly and other extension errors
  if (event.error?.stack?.includes('content.js') ||
      event.error?.stack?.includes('Grammarly') ||
      event.message?.includes('viewBox') ||
      event.message?.includes('not valid JSON')) {
    console.warn('Browser extension error suppressed:', event.error?.message);
    event.preventDefault();
    return false;
  }
});

window.addEventListener('unhandledrejection', (event) => {
  // Suppress JSON parsing errors from extensions
  if (event.reason?.message?.includes('not valid JSON') ||
      event.reason?.message?.includes('Unexpected token')) {
    console.warn('Extension JSON error suppressed:', event.reason?.message);
    event.preventDefault();
    return false;
  }
});

// Debug info for Vercel deployment with error handling
try {
  console.log('Starting application...')
  console.log('Environment:', import.meta.env?.MODE || 'unknown')
  console.log('Base URL:', import.meta.env?.BASE_URL || 'unknown')
} catch (error) {
  console.warn('Error accessing environment variables:', error);
}

// Function to render fallback error UI if React fails to load
function renderErrorFallback(error: any) {
  console.error("RENDERING ERROR FALLBACK:", error);
  
  // Hide loader
  const loader = document.getElementById('initial-loader');
  if (loader) {
    loader.style.display = 'none';
  }
  
  const rootDiv = document.getElementById('root');
  if (rootDiv) {
    rootDiv.innerHTML = `
      <div style="font-family: sans-serif; padding: 20px; color: #721c24; background: #f8d7da; border-radius: 5px;">
        <h2>Something went wrong</h2>
        <p>The application failed to initialize properly.</p>
        <p>Error: ${error?.message || 'Unknown error'}</p>
        <div style="margin-top: 20px; padding: 10px; background: #f1f1f1; border-radius: 4px; overflow: auto;">
          <pre>${error?.stack || 'No stack trace available'}</pre>
        </div>
        <button onclick="window.location.reload()" style="background-color: #3b82f6; color: white; border: none; padding: 8px 16px; border-radius: 4px; cursor: pointer; margin-top: 10px;">Reload Page</button>
      </div>
    `;
  }
}

// Function to check database health and show status UI
const checkDatabaseHealth = async (attempt = 1) => {
  // Only check during development or when specifically enabled
  try {
    if (!import.meta.env.DEV && !import.meta.env.VITE_ENABLE_HEALTH_CHECKS) {
      console.log('Database health checks disabled in production');
      return { status: 'healthy', reason: 'Health checks disabled in production' };
    }
  } catch (envError) {
    console.warn('Error accessing environment variables:', envError);
  }

  try {
    const health = await Promise.race([
      checkSupabaseHealth(),
      // Add a timeout to prevent waiting too long (reduced to 6 seconds)
      new Promise<any>(resolve => setTimeout(() =>
        resolve({
          status: 'degraded',
          reason: 'Health check timeout',
          suggestion: 'Taking too long to check database status. Continuing with limited functionality.'
        }), 6000)
      )
    ]);
    
    console.log('Database health check result:', health);
    
    // Handle different health statuses
    if (health.status === 'healthy') {
      // Database is healthy, continue with app initialization
      // No notification needed
    } 
    else if (health.status === 'degraded') {
      // Only show toast for degraded status if it's not due to timeout or recent failure caching
      if (!health.reason?.includes('Recent connection failure') && !health.reason?.includes('timeout')) {
        toast({
          title: 'Limited Connection',
          description: 'Some features may load slower than usual or be unavailable.',
          variant: 'default',
          duration: 5000,
        });
      }

      // Continue app initialization - don't block on degraded service
    }
    else {
      // Status is unhealthy - but be less aggressive about retries and notifications
      if (attempt <= 1) {  // Only retry once to speed up initialization
        const retryDelay = 2000; // Fixed 2 second delay
        console.log(`Retrying database health check in ${retryDelay}ms (attempt ${attempt})`);

        setTimeout(() => {
          checkDatabaseHealth(attempt + 1);
        }, retryDelay);

        // Don't return, continue with app initialization even while retrying
      } else {
        // Only show notification after retries are exhausted
        toast({
          title: 'Connection Issues',
          description: health.suggestion || 'Having trouble connecting to our services. Some features may be unavailable.',
          variant: 'destructive',
          duration: 8000,
          action: <ToastAction altText="Try again" onClick={() => checkDatabaseHealth(1)}>Try Again</ToastAction>,
        });
      }
    }
    
    return health;
  } catch (error) {
    console.error('Error checking database health:', error);
    
    // Show a notification but don't block the app
    toast({
      title: 'Connection Check Failed',
      description: 'Could not verify connection status. The app will continue to function normally.',
      variant: 'default',
      duration: 5000,
    });
    
    return { status: 'unknown', reason: 'Check failed' };
  }
};

// Wrap everything in a try-catch to catch any module loading errors
try {
  // Find the root element
  const rootElement = document.getElementById('root');
  if (!rootElement) {
    throw new Error('Root element with id "root" not found in the document');
  }

  console.log('Creating root...');
  // Create the React root
  const root = createRoot(rootElement);

  console.log('Root created, rendering app...');

  // Fallback timeout to hide loader if something goes wrong
  const fallbackTimeout = setTimeout(() => {
    const loader = document.getElementById('initial-loader');
    if (loader) {
      console.warn('Fallback: Hiding loader after 15 seconds');
      loader.style.display = 'none';
    }
  }, 15000); // 15 second fallback

  // Clear any stale failure timestamps on app start
  const lastFailure = localStorage.getItem('supabase_last_failure');
  if (lastFailure) {
    const failureAge = Date.now() - parseInt(lastFailure);
    if (failureAge > 5 * 60 * 1000) { // Clear failures older than 5 minutes
      localStorage.removeItem('supabase_last_failure');
      console.log('Cleared stale Supabase failure timestamp');
    }
  }

  // Render the app immediately for faster startup
  try {
    console.log('Rendering App immediately...');
    root.render(
      <React.StrictMode>
        <App />
      </React.StrictMode>
    );
    console.log('Application rendered successfully');

    // Hide loader on successful render - wait a bit longer to ensure app is ready
    const loader = document.getElementById('initial-loader');
    if (loader) {
      setTimeout(() => {
        // Double check that the app has actually rendered
        const appRoot = document.getElementById('root');
        if (appRoot && appRoot.children.length > 1) {
          loader.style.display = 'none';
          clearTimeout(fallbackTimeout);
          console.log('Initial loader hidden - app is ready');
        } else {
          console.log('App not ready yet, keeping loader visible');
          // Try again in a bit
          setTimeout(() => {
            loader.style.display = 'none';
            clearTimeout(fallbackTimeout);
          }, 1000);
        }
      }, 1000); // Increased to 1 second to ensure app is fully loaded
    }

    // Check database health in background after app is rendered
    checkDatabaseHealth()
      .then(() => {
        console.log('Background database check complete');
      })
      .catch(dbError => {
        console.warn('Background database check failed:', dbError);
        // App is already running, so this is just a warning
      });

  } catch (renderError: any) {
    console.error('Error in React rendering:', renderError);
    renderErrorFallback(renderError);
  }
} catch (error: any) {
  console.error('Fatal error initializing application:', error);
  renderErrorFallback(error);
}
