import React from 'react';
import { BrowserRouter as Router, Routes, Route, Navigate, useParams, useLocation } from 'react-router-dom';
import { useEffect, useState, Suspense, Component, ReactNode } from 'react';
import { AuthProvider } from '@/context/AuthContext';
import { OnboardingProvider } from '@/context/OnboardingContext';
import { Toaster } from 'sonner';
import { ThemeProvider } from '@/components/ui/theme-provider';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { HelmetProvider } from 'react-helmet-async';
import { useUser } from '@/hooks/useUsers';
import { useCategories } from '@/hooks/useCategories';
import { useAuth } from '@/context/AuthContext';
import { supabase } from '@/integrations/supabase/client';
import { toast } from 'sonner';
import { useNavigate } from 'react-router-dom';
import PageContainer from '@/components/layout/PageContainer';
import { useInfluencerCoupons } from '@/hooks/useCoupons';
import { useRef } from 'react';
import RouteGuard from '@/components/guards/RouteGuard';
import MainLayout from '@/components/layout/MainLayout';
import { initializeSession, cleanupLocalStorage } from '@/utils/localStorageHelpers';

// Pages
import Home from '@/pages/HomePage';
import Auth from '@/pages/auth/Auth';
import ResetPassword from '@/pages/auth/ResetPassword';
import Settings from '@/pages/user/Settings';
import UserProfile from '@/pages/user/UserProfile';
import PremiumDeals from "@/pages/PremiumDeals";
import SavedCouponsPage from '@/pages/user/SavedCoupons';
import ProfilePreview from '@/pages/user/ProfilePreview';
import CreateCoupon from '@/pages/coupon/CreateCoupon';
import Coupons from '@/pages/coupon/Coupons';
import PublicCoupons from '@/pages/coupon/PublicCoupons';
import CouponDetail from '@/pages/coupon/CouponDetail';
import AnalyticsDashboard from '@/pages/analytics/AnalyticsDashboard';
import NotFound from '@/pages/NotFound';
import TrendingDeals from '@/pages/TrendingDeals';
import HelpCenter from '@/pages/Help';
import Explore from '@/pages/content/Explore';
import Category from '@/pages/content/Category';
import Brands from '@/pages/brand/Brands';
import BrandDetail from '@/pages/brand/BrandDetail';
import CreateBrand from '@/pages/brand/CreateBrand';
import Transactions from '@/pages/user/Transactions';
import SharedProfile from '@/pages/user/SharedProfile';
import Blog from '@/pages/content/Blog';
import Terms from '@/pages/legal/Terms';
import Privacy from '@/pages/legal/Privacy';
import Contact from '@/pages/Contact';
import SearchPage from '@/pages/content/SearchPage';
import BlackFridayDeals from '@/pages/deals/BlackFridayDeals';
import CyberMondayDeals from '@/pages/deals/CyberMondayDeals';
import HolidayDeals from '@/pages/deals/HolidayDeals';
import SubcategoryDeals from '@/pages/categories/SubcategoryDeals';
import SeasonalShoppingGuide from '@/pages/blog/SeasonalShoppingGuide';

// Landing page components
import LandingHero from '@/components/landing/LandingHero';
import HowToUse from '@/components/landing/HowToUse';
import CreatorsGallery from '@/components/landing/CreatorsGallery';
import FeatureHighlight from '@/components/landing/FeatureHighlight';
import Footer from '@/components/landing/Footer';

// Create an optimized query client instance with performance-focused settings
const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      retry: 3,
      retryDelay: attemptIndex => Math.min(1000 * 2 ** attemptIndex, 30000),
      staleTime: 1000 * 60 * 5, // 5 minutes default
      gcTime: 1000 * 60 * 30, // 30 minutes cache time
      refetchOnWindowFocus: false,
      refetchOnMount: true, // Enable refetch on mount to ensure data loads
      refetchOnReconnect: 'always', // Refetch when reconnecting
      networkMode: 'online', // Only run queries when online
    },
    mutations: {
      retry: 2,
      retryDelay: attemptIndex => Math.min(1000 * 2 ** attemptIndex, 10000),
      networkMode: 'online',
    },
  },
});

// Create a fallback component for connection issues
interface ErrorFallbackProps {
  error: Error;
}

const ErrorFallback = ({ error }: ErrorFallbackProps) => (
  <div className="min-h-screen flex flex-col items-center justify-center bg-white p-4">
    <div className="max-w-md w-full bg-red-50 border border-red-200 rounded-lg p-6 text-center">
      <h2 className="text-2xl font-bold text-red-700 mb-4">Connection Error</h2>
      <p className="mb-4 text-gray-700">
        We're having trouble connecting to our servers. This could be due to:
      </p>
      <ul className="text-left text-sm text-gray-700 mb-6 space-y-1">
        <li>• Temporary network issues</li>
        <li>• Server maintenance</li>
        <li>• Your internet connection</li>
      </ul>
      <p className="text-sm text-gray-600 mb-6">{error.message}</p>
      <button 
        onClick={() => window.location.reload()} 
        className="px-4 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600 transition-colors"
      >
        Try Again
      </button>
    </div>
  </div>
);

// Loading component
const Loading = () => (
  <div className="min-h-screen flex items-center justify-center bg-white">
    <div className="text-center">
      <div className="w-16 h-16 border-4 border-blue-200 border-t-blue-500 rounded-full animate-spin mx-auto"></div>
      <p className="mt-4 text-gray-600">Loading CouponLink...</p>
    </div>
  </div>
);

// Custom error boundary component
interface ErrorBoundaryState {
  hasError: boolean;
  error: Error | null;
}

interface ErrorBoundaryProps {
  children: ReactNode;
}

class AppErrorBoundary extends Component<ErrorBoundaryProps, ErrorBoundaryState> {
  constructor(props: ErrorBoundaryProps) {
    super(props);
    this.state = { hasError: false, error: null };
  }

  static getDerivedStateFromError(error: Error): ErrorBoundaryState {
    return { hasError: true, error };
  }

  componentDidCatch(error: Error, errorInfo: React.ErrorInfo): void {
    // Check if this is a browser extension error that we should ignore
    const isExtensionError =
      error.message?.includes('viewBox') ||
      error.message?.includes('Grammarly') ||
      error.message?.includes('content.js') ||
      error.stack?.includes('extension') ||
      error.stack?.includes('content.js') ||
      error.message?.includes('not valid JSON');

    if (isExtensionError) {
      console.warn('Browser extension error detected and ignored:', error.message);
      // Reset the error boundary for extension errors
      this.setState({ hasError: false, error: null });
      return;
    }

    console.error("App error boundary caught error:", error, errorInfo);
  }

  render(): ReactNode {
    if (this.state.hasError) {
      return <ErrorFallback error={this.state.error!} />;
    }

    return this.props.children;
  }
}

// DirectUrlHandler component to handle direct username URLs
const DirectUrlHandler = () => {
  const { username } = useParams();
  const location = useLocation();
  const [loading, setLoading] = useState(true);
  const { user: currentUser } = useAuth();
  const shareSectionRef = useRef(null);

  // Check if the username exists
  const { data: user, isLoading: userLoading } = useUser(username || '');
  const { data: categories, isLoading: categoryLoading } = useCategories();
  // Fetch user's coupons
  const { data: coupons, isLoading: couponsLoading } = useInfluencerCoupons(user?.id || '');

  useEffect(() => {
    const checkAndRedirect = async () => {
      // Handle system routes immediately without waiting for data
      const systemRoutes = ['home', 'auth', 'settings', 'saved', 'profile'];
      if (username && systemRoutes.includes(username.toLowerCase())) {
        setLoading(false);
        return;
      }

      // Set loading to false faster - don't wait for all data
      if (!userLoading) {
        setLoading(false);
      }
    };

    checkAndRedirect();
  }, [username, userLoading]); // Removed categoryLoading and couponsLoading dependencies

  // Only show loading for user data, not categories/coupons
  if (loading || userLoading) {
    return <Loading />;
  }

  // If user exists, determine which view to show
  if (user) {
    const profileUrl = `${window.location.origin}/${username}`;
    
    // Always show shared profile for direct username URLs
    return <SharedProfile 
      user={user}
      userLoading={userLoading}
      coupons={coupons || []}
      couponsLoading={couponsLoading}
      profileUrl={profileUrl}
      copyToClipboard={() => {}}
      shareProfile={() => {}}
      copied={false}
      shouldHighlightShare={false}
      shareSectionRef={shareSectionRef}
    />;
  }

  // If no matches, redirect to home
  return <Navigate to="/home" />;
};

// Create Landing page component
const Landing = () => {
  const [username, setUsername] = useState('');
  const [isChecking, setIsChecking] = useState(false);
  const [isAvailable, setIsAvailable] = useState<boolean | null>(null);
  const usernameFormRef = React.useRef<HTMLDivElement>(null);
  const navigate = useNavigate();
  
  const handleClaimUsername = async () => {
    if (!username.trim()) return;
    
    setIsChecking(true);
    
    try {
      // Check if username exists in profiles table
      const { count, error } = await supabase
        .from('profiles')
        .select('*', { count: 'exact', head: true })
        .ilike('username', username.trim());
      
      if (error) {
        console.error('Error checking username:', error);
        toast.error('Failed to check username availability');
        return;
      }
      
      const available = count === 0;
      setIsAvailable(available);
      
      // If username is available, redirect to signup page with username as parameter
      if (available) {
        setTimeout(() => {
          navigate(`/auth?mode=signup&username=${encodeURIComponent(username)}`);
        }, 500); // Short delay to show the availability message
      }
    } catch (error) {
      console.error('Error checking username:', error);
      toast.error('Failed to check username availability');
    } finally {
      setIsChecking(false);
    }
  };
  
  return (
    <MainLayout showTaskbar={false}>
      <PageContainer 
        fullWidth={true} 
        decorationType="landing" 
        customBackground="bg-white dark:bg-gray-950"
        className="p-0"
      >
        <LandingHero 
          username={username}
          setUsername={setUsername}
          isChecking={isChecking}
          isAvailable={isAvailable}
          handleClaimUsername={handleClaimUsername}
          usernameFormRef={usernameFormRef}
        />
        <FeatureHighlight />
        <HowToUse />
        <CreatorsGallery />
        <Footer />
      </PageContainer>
    </MainLayout>
  );
};

// SharedProfileWrapper component to handle data fetching
const SharedProfileWrapper = () => {
  const { username } = useParams();
  const { data: user, isLoading: userLoading } = useUser(username || '');
  const { data: coupons, isLoading: couponsLoading } = useInfluencerCoupons(user?.id || '');
  const shareSectionRef = useRef<HTMLDivElement>(null);
  const [shouldHighlightShare, setShouldHighlightShare] = useState(false);
  const [copied, setCopied] = useState(false);

  const profileUrl = `${window.location.origin}/${username}`;

  const copyToClipboard = async () => {
    try {
      await navigator.clipboard.writeText(profileUrl);
      setCopied(true);
      setTimeout(() => setCopied(false), 2000);
    } catch (error) {
      console.error('Error copying to clipboard:', error);
    }
  };

  const shareProfile = () => {
    if (navigator.share) {
      navigator.share({
        title: `${username}'s Profile`,
        text: `Check out ${username}'s coupons and deals!`,
        url: profileUrl,
      }).catch(console.error);
    } else {
      copyToClipboard();
    }
  };

  // Only wait for user data, load coupons in background
  if (userLoading) {
    return <Loading />;
  }

  if (!user) {
    return <Navigate to="/404" />;
  }

  return (
    <SharedProfile
      user={user}
      userLoading={userLoading}
      coupons={coupons || []}
      couponsLoading={couponsLoading}
      profileUrl={profileUrl}
      copyToClipboard={copyToClipboard}
      shareProfile={shareProfile}
      copied={copied}
      shouldHighlightShare={shouldHighlightShare}
      shareSectionRef={shareSectionRef}
    />
  );
};

// CreateCouponWrapper component to handle onboarding context
const CreateCouponWrapper = () => {
  // Simplify - the OnboardingBanner in MainLayout will handle displaying the right UI
  return <CreateCoupon />;
};

// Add a ProfileWrapper component to preserve onboarding state
const ProfileWrapper = () => {
  // Simplify - the OnboardingBanner in MainLayout will handle displaying the right UI
  return <UserProfile />;
};

// Add an AnalyticsWrapper component to preserve onboarding state
const AnalyticsWrapper = () => {
  // Simplify - the OnboardingBanner in MainLayout will handle displaying the right UI
  return <AnalyticsDashboard />;
};

function App() {
  useEffect(() => {
    // Clean up any corrupted localStorage data first
    cleanupLocalStorage();
    // Then initialize session
    initializeSession();
  }, []);

  return (
    <Router>
      <QueryClientProvider client={queryClient}>
      <AppErrorBoundary>
          <HelmetProvider>
            <ThemeProvider defaultTheme="light" storageKey="coupon-theme">
            <AuthProvider>
              <OnboardingProvider>
                <Suspense fallback={<Loading />}>
                  <Routes>
                    {/* Public routes */}
                    <Route path="/" element={<Landing />} />
                    <Route path="/auth" element={<Auth />} />
                    <Route path="/reset-password" element={<ResetPassword />} />
                    <Route path="/username-check" element={<Auth />} />
                    <Route path="/browse-coupons" element={<PublicCoupons />} />

                    {/* Onboarding using popups now - no dedicated routes needed */}

                    {/* Protected routes */}
                    <Route path="/home" element={
                      <RouteGuard requireAuth>
                        <Home />
                      </RouteGuard>
                    } />
                    <Route path="/settings" element={
                      <RouteGuard>
                        <Settings />
                      </RouteGuard>
                    } />
                    <Route path="/saved" element={
                      <RouteGuard>
                        <SavedCouponsPage />
                      </RouteGuard>
                    } />
                    <Route path="/profile" element={<RouteGuard requireAuth><UserProfile /></RouteGuard>} />
                    <Route path="/profile/preview" element={<RouteGuard requireAuth><ProfilePreview /></RouteGuard>} />
                    <Route path="/:username/share" element={<ProfilePreview />} />
                    <Route path="/coupons" element={
                      <RouteGuard>
                        <Coupons />
                      </RouteGuard>
                    } />
                    <Route path="/create-coupon" element={
                      <RouteGuard>
                        <CreateCouponWrapper />
                      </RouteGuard>
                    } />
                    <Route path="/coupon/:id" element={<CouponDetail />} />
                    <Route path="/analytics" element={
                      <RouteGuard>
                          <AnalyticsWrapper />
                      </RouteGuard>
                    } />
                    <Route path="/premium" element={
                      <RouteGuard>
                        <PremiumDeals />
                      </RouteGuard>
                    } />
                    <Route path="/trending" element={<TrendingDeals />} />
                    <Route path="/help" element={<HelpCenter />} />
                    <Route path="/explore" element={<Explore />} />
                    <Route path="/search" element={<SearchPage />} />
                    <Route path="/blog" element={<Blog />} />
                    <Route path="/blog/seasonal-shopping-guide" element={<SeasonalShoppingGuide />} />
                    <Route path="/terms" element={<Terms />} />
                    <Route path="/privacy" element={<Privacy />} />
                    <Route path="/contact" element={<Contact />} />
                    <Route path="/categories" element={<Category />} />
                    <Route path="/categories/:slug" element={<Category />} />
                    <Route path="/categories/:category/:subcategory" element={<SubcategoryDeals />} />
                    <Route path="/deals/black-friday" element={<BlackFridayDeals />} />
                    <Route path="/deals/cyber-monday" element={<CyberMondayDeals />} />
                    <Route path="/deals/holiday" element={<HolidayDeals />} />
                    <Route path="/brands" element={<Brands />} />
                    <Route path="/brands/create" element={
                      <RouteGuard>
                        <CreateBrand />
                      </RouteGuard>
                    } />
                    <Route path="/brands/:id" element={<BrandDetail />} />
                    <Route path="/transactions" element={
                      <RouteGuard>
                        <Transactions />
                      </RouteGuard>
                    } />
                    <Route path="/user/transactions" element={
                      <RouteGuard>
                        <Transactions />
                      </RouteGuard>
                    } />
                    <Route path="/user/saved-coupons" element={
                      <RouteGuard>
                        <SavedCouponsPage />
                      </RouteGuard>
                    } />
                    <Route path="/shared/:username" element={<SharedProfileWrapper />} />
                    
                    {/* Direct username URLs */}
                    <Route path="/:username" element={<DirectUrlHandler />} />
                    
                    {/* 404 route */}
                    <Route path="*" element={<NotFound />} />
                  </Routes>
                </Suspense>
                <Toaster position="top-center" />
              </OnboardingProvider>
            </AuthProvider>
          </ThemeProvider>
          </HelmetProvider>
        </AppErrorBoundary>
        </QueryClientProvider>
    </Router>
  );
}

export default App;
