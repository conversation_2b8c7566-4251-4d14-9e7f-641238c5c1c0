import React from 'react';

interface EnhancedLoadingProps {
  message?: string;
  submessage?: string;
  size?: 'sm' | 'md' | 'lg';
  variant?: 'default' | 'minimal' | 'branded';
  className?: string;
}

const EnhancedLoading: React.FC<EnhancedLoadingProps> = ({
  message = "Loading",
  submessage = "Please wait",
  size = 'md',
  variant = 'default',
  className = ''
}) => {
  const sizeClasses = {
    sm: {
      container: 'p-4',
      logo: 'text-xl mb-4',
      spinner: 'w-8 h-8 border-2',
      text: 'text-sm',
      subtext: 'text-xs'
    },
    md: {
      container: 'p-6',
      logo: 'text-2xl mb-6',
      spinner: 'w-10 h-10 border-3',
      text: 'text-base',
      subtext: 'text-sm'
    },
    lg: {
      container: 'p-8',
      logo: 'text-3xl mb-8',
      spinner: 'w-12 h-12 border-3',
      text: 'text-lg',
      subtext: 'text-base'
    }
  };

  const classes = sizeClasses[size];

  if (variant === 'minimal') {
    return (
      <div className={`flex items-center justify-center ${className}`}>
        <div className="text-center">
          <div className={`${classes.spinner} border-gray-200 border-t-[#0ea76b] rounded-full animate-spin mx-auto mb-3`}></div>
          <p className={`${classes.text} text-gray-600 dark:text-gray-400 font-medium`}>
            {message}
          </p>
          {submessage && (
            <p className={`${classes.subtext} text-gray-400 dark:text-gray-500 mt-1`}>
              {submessage}
            </p>
          )}
        </div>
      </div>
    );
  }

  if (variant === 'branded') {
    return (
      <div className={`min-h-screen flex items-center justify-center bg-white dark:bg-gray-900 ${className}`}>
        <div className={`text-center ${classes.container}`}>
          {/* Logo */}
          <div className="mb-8">
            <h1 className={`${classes.logo} font-semibold text-[#0ea76b]`}>
              CouponLink
            </h1>
          </div>

          {/* Simple spinner */}
          <div className="mb-6">
            <div className={`${classes.spinner} border-gray-200 border-t-[#0ea76b] rounded-full animate-spin mx-auto`}></div>
          </div>

          {/* Loading text */}
          <div className="space-y-2">
            <p className={`${classes.text} font-medium text-gray-700 dark:text-gray-300`}>
              {message}
            </p>
            <p className={`${classes.subtext} text-gray-500 dark:text-gray-400`}>
              {submessage}
            </p>
          </div>
        </div>
      </div>
    );
  }

  // Default variant
  return (
    <div className={`flex items-center justify-center bg-white dark:bg-gray-900 ${className}`}>
      <div className={`text-center ${classes.container}`}>
        {/* Simple spinner */}
        <div className="mb-6">
          <div className={`${classes.spinner} border-gray-200 border-t-[#0ea76b] rounded-full animate-spin mx-auto`}></div>
        </div>

        {/* Loading text */}
        <div className="space-y-2">
          <p className={`${classes.text} font-medium text-gray-700 dark:text-gray-300`}>
            {message}
          </p>
          <p className={`${classes.subtext} text-gray-500 dark:text-gray-400`}>
            {submessage}
          </p>
        </div>
      </div>
    </div>
  );
};

export default EnhancedLoading;
