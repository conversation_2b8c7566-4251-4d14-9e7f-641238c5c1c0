import React from 'react';

interface EnhancedLoadingProps {
  message?: string;
  submessage?: string;
  size?: 'sm' | 'md' | 'lg';
  variant?: 'default' | 'minimal' | 'branded';
  className?: string;
}

const EnhancedLoading: React.FC<EnhancedLoadingProps> = ({
  message = "Loading",
  submessage = "Please wait while we prepare your content",
  size = 'md',
  variant = 'default',
  className = ''
}) => {
  const sizeClasses = {
    sm: {
      container: 'p-4',
      logo: 'text-2xl mb-4',
      spinner: 'w-12 h-12',
      text: 'text-base',
      subtext: 'text-sm'
    },
    md: {
      container: 'p-6',
      logo: 'text-3xl mb-6',
      spinner: 'w-16 h-16',
      text: 'text-lg',
      subtext: 'text-sm'
    },
    lg: {
      container: 'p-8',
      logo: 'text-4xl mb-8',
      spinner: 'w-20 h-20',
      text: 'text-xl',
      subtext: 'text-base'
    }
  };

  const classes = sizeClasses[size];

  if (variant === 'minimal') {
    return (
      <div className={`flex items-center justify-center ${className}`}>
        <div className="text-center">
          <div className={`${classes.spinner} border-3 border-gray-200 border-t-blue-600 rounded-full animate-spin mx-auto mb-3`}></div>
          <p className={`${classes.text} text-gray-600 dark:text-gray-400 animate-pulse`}>
            {message}
            <span className="inline-block animate-bounce ml-1">.</span>
            <span className="inline-block animate-bounce ml-1" style={{animationDelay: '0.1s'}}>.</span>
            <span className="inline-block animate-bounce ml-1" style={{animationDelay: '0.2s'}}>.</span>
          </p>
        </div>
      </div>
    );
  }

  if (variant === 'branded') {
    return (
      <div className={`min-h-screen flex items-center justify-center bg-gradient-to-br from-blue-50 to-indigo-100 dark:from-gray-900 dark:to-gray-800 ${className}`}>
        <div className={`text-center ${classes.container}`}>
          {/* Logo */}
          <div className="mb-8">
            <h1 className={`${classes.logo} font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent`}>
              CouponLink
            </h1>
          </div>
          
          {/* Enhanced spinner */}
          <div className="relative mb-6">
            <div className={`${classes.spinner} border-4 border-blue-200 border-t-blue-600 rounded-full animate-spin mx-auto shadow-lg`}></div>
            <div className={`absolute inset-0 ${classes.spinner} border-4 border-transparent border-r-purple-400 rounded-full animate-spin mx-auto`} style={{animationDirection: 'reverse', animationDuration: '1.5s'}}></div>
          </div>
          
          {/* Loading text with animation */}
          <div className="space-y-2">
            <p className={`${classes.text} font-semibold text-gray-700 dark:text-gray-300 animate-pulse`}>
              {message}
              <span className="inline-block animate-bounce ml-1">.</span>
              <span className="inline-block animate-bounce ml-1" style={{animationDelay: '0.1s'}}>.</span>
              <span className="inline-block animate-bounce ml-1" style={{animationDelay: '0.2s'}}>.</span>
            </p>
            <p className={`${classes.subtext} text-gray-500 dark:text-gray-400`}>
              {submessage}
            </p>
          </div>
          
          {/* Progress bar */}
          <div className="mt-6 w-64 mx-auto">
            <div className="h-2 bg-gray-200 dark:bg-gray-700 rounded-full overflow-hidden">
              <div className="h-full bg-gradient-to-r from-blue-500 to-purple-500 rounded-full animate-pulse"></div>
            </div>
          </div>
        </div>
      </div>
    );
  }

  // Default variant
  return (
    <div className={`flex items-center justify-center bg-gradient-to-br from-blue-50 to-indigo-100 dark:from-gray-900 dark:to-gray-800 ${className}`}>
      <div className={`text-center ${classes.container}`}>
        {/* Enhanced spinner */}
        <div className="relative mb-6">
          <div className={`${classes.spinner} border-4 border-blue-200 border-t-blue-600 rounded-full animate-spin mx-auto shadow-lg`}></div>
          <div className={`absolute inset-0 ${classes.spinner} border-4 border-transparent border-r-purple-400 rounded-full animate-spin mx-auto`} style={{animationDirection: 'reverse', animationDuration: '1.5s'}}></div>
        </div>
        
        {/* Loading text with animation */}
        <div className="space-y-2">
          <p className={`${classes.text} font-semibold text-gray-700 dark:text-gray-300 animate-pulse`}>
            {message}
            <span className="inline-block animate-bounce ml-1">.</span>
            <span className="inline-block animate-bounce ml-1" style={{animationDelay: '0.1s'}}>.</span>
            <span className="inline-block animate-bounce ml-1" style={{animationDelay: '0.2s'}}>.</span>
          </p>
          <p className={`${classes.subtext} text-gray-500 dark:text-gray-400`}>
            {submessage}
          </p>
        </div>
      </div>
    </div>
  );
};

export default EnhancedLoading;
