import { useNavigate } from 'react-router-dom';
import { useOnboarding } from '@/context/OnboardingContext';
import { Button } from '@/components/ui/button';
import { COLORS } from '@/constants/theme';
import { ArrowRight, X } from 'lucide-react';
import { motion, AnimatePresence } from 'framer-motion';
import { toast } from 'sonner';

// Import CSS
import './OnboardingModal.css';

interface OnboardingModalProps {
  isOpen: boolean;
  onClose: () => void;
}

/**
 * OnboardingModal component
 * Displays a popup modal with instructions for the current onboarding step
 */
const OnboardingModal = ({ isOpen, onClose }: OnboardingModalProps) => {
  const {
    currentStep,
    isOnboardingComplete,
    skipStep,
    handleStepCompletion,
    getCurrentMessage
  } = useOnboarding();
  const navigate = useNavigate();



  // Get current message for the step
  const message = getCurrentMessage();
  
  // <PERSON>le continuing to next step
  const handleContinue = async () => {
    try {
      console.log('OnboardingModal: Continue clicked for step:', currentStep);

      // Close modal first to prevent multiple clicks
      onClose();

      // For the profile_setup step, we don't need any additional data
      // The user will have already filled out their profile before clicking continue
      if (currentStep === 'profile_setup') {
        // Don't pass any data, just complete the step
        await handleStepCompletion(currentStep);
      } else {
        // For other steps, just complete the step with no data
        await handleStepCompletion(currentStep);
      }

      console.log('OnboardingModal: Step completion successful');

    } catch (error: any) {
      console.error('Failed to complete step:', error);
      if (error?.message) console.error('Error message:', error.message);
      toast.error(`Failed to complete this step: ${error?.message || 'Unknown error'}`);
    }
  };
  
  // Handle skipping the current step
  const handleSkip = async () => {
    try {
      console.log('OnboardingModal: Skip clicked for step:', currentStep);

      // Close modal first to prevent multiple clicks
      onClose();

      // Skip to the next step
      await skipStep();

      console.log('OnboardingModal: Skip successful');

    } catch (error) {
      console.error('Failed to skip step:', error);
      toast.error('Failed to skip this step');
    }
  };



  // If we shouldn't show the modal, don't render anything
  if (!isOpen || isOnboardingComplete || !message) {
    return null;
  }

  return (
    <AnimatePresence>
      {isOpen && (
        <>
          {/* Backdrop */}
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="fixed inset-0 bg-black/50 z-[59]"
            onClick={onClose}
          />

          {/* Modal */}
          <motion.div
            initial={{ opacity: 0, scale: 0.9 }}
            animate={{ opacity: 1, scale: 1 }}
            exit={{ opacity: 0, scale: 0.9 }}
            className="onboarding-modal fixed z-[60] w-[90%] max-w-[300px] sm:max-w-[350px] p-3 sm:p-4 rounded-lg sm:rounded-xl shadow-xl overflow-hidden flex flex-col max-h-[80vh]"
            style={{ 
              background: `linear-gradient(135deg, ${COLORS.surface.light}, ${COLORS.surface.lightTransparent})`,
              border: `1px solid rgba(255, 255, 255, 0.3)`,
              backdropFilter: 'blur(10px)'
            }}
          >
            {/* Close button */}
            <button
              className="absolute top-1 right-1 sm:top-2 sm:right-2 text-gray-500 hover:text-gray-700 z-10"
              onClick={onClose}
              aria-label="Close"
            >
              <X size={12} className="sm:h-4 sm:w-4" />
            </button>
            
            {/* Content */}
            <div className="mb-2 sm:mb-3 overflow-y-auto pb-1 flex-1 px-1 mt-3 sm:mt-1">
              <div 
                className="w-8 h-8 sm:w-10 sm:h-10 mb-2 rounded-full flex items-center justify-center mx-auto"
                style={{ 
                  background: `linear-gradient(135deg, ${COLORS.primary.main}, ${COLORS.primary.dark})`,
                  boxShadow: `0 4px 10px rgba(0, 0, 0, 0.1)`
                }}
              >
                <span className="text-white text-sm sm:text-base font-bold">
                  {currentStep === 'welcome' ? '1' :
                   currentStep === 'website_tour' ? '2' :
                   currentStep === 'start_menu_intro' ? '3' :
                   currentStep === 'start_menu_features' ? '4' :
                   currentStep === 'explore_brands' ? '5' :
                   currentStep === 'explore_categories' ? '6' :
                   currentStep === 'profile_setup' ? '7' :
                   currentStep === 'payment_setup' ? '8' :
                   currentStep === 'first_coupon' ? '9' : '✓'}
                </span>
              </div>
              <h3 
                className="text-xs sm:text-sm font-bold text-center mb-1"
                style={{ color: COLORS.neutral[800] }}
              >
                {message.title}
              </h3>
              <p 
                className="text-[10px] sm:text-xs text-center"
                style={{ color: COLORS.neutral[600] }}
              >
                {message.description}
              </p>


            </div>
            
            {/* Actions */}
            <div className="flex justify-between mt-1 sm:mt-2 gap-1 sm:gap-2">
              <Button
                variant="outline"
                onClick={handleSkip}
                className="px-2 text-[9px] sm:text-xs min-w-0 h-6 sm:h-7"
                size="sm"
              >
                Skip
              </Button>
              <Button
                onClick={handleContinue}
                className="px-2 text-[9px] sm:text-xs h-6 sm:h-7"
                size="sm"
                style={{ 
                  background: `linear-gradient(135deg, ${COLORS.primary.main}, ${COLORS.primary.dark})`,
                  color: 'white'
                }}
              >
                Continue <ArrowRight size={8} className="ml-1 sm:ml-1.5 sm:h-3 sm:w-3" />
              </Button>
            </div>
          </motion.div>
        </>
      )}
    </AnimatePresence>
  );
};

export default OnboardingModal; 